#!/usr/bin/env python3
"""
Simple HTTP server for CTI Dashboard frontend
"""

import http.server
import socketserver
import webbrowser
import os
import sys
from pathlib import Path

PORT = 8080
Handler = http.server.SimpleHTTPRequestHandler

# Change to frontend directory
frontend_dir = Path(__file__).parent / "src" / "frontend"
if frontend_dir.exists():
    os.chdir(frontend_dir)
    print(f"📁 Serving from: {frontend_dir}")
else:
    print(f"❌ Frontend directory not found: {frontend_dir}")
    sys.exit(1)

try:
    with socketserver.TCPServer(("", PORT), Handler) as httpd:
        print(f"🌐 Frontend server running at: http://localhost:{PORT}")
        print(f"📚 Main dashboard: http://localhost:{PORT}/index.html")
        print(f"🔧 Backend API: http://localhost:8000")
        print("🔧 Press Ctrl+C to stop")
        
        # Try to open browser
        try:
            webbrowser.open(f"http://localhost:{PORT}/index.html")
        except:
            pass
            
        httpd.serve_forever()
        
except KeyboardInterrupt:
    print("\n👋 Frontend server stopped")
except Exception as e:
    print(f"❌ Error: {e}")

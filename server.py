#!/usr/bin/env python3
"""
CTI Dashboard Backend Server - Main Entry Point
This is the single main entry point for the CTI Dashboard backend server.
"""

import sys
import os
import logging
from pathlib import Path

# Add the backend directory to Python path
backend_dir = Path(__file__).parent / "src" / "backend"
if str(backend_dir) not in sys.path:
    sys.path.insert(0, str(backend_dir))

# Also add the original backend directory for compatibility
original_backend_dir = Path(__file__).parent / "backend"
if str(original_backend_dir) not in sys.path:
    sys.path.insert(0, str(original_backend_dir))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def main():
    """Main entry point for the CTI Dashboard backend server"""

    print("🛡️ CTI Dashboard Backend Server")
    print("=" * 50)
    print("🚀 Starting Cyber Threat Intelligence Dashboard API...")
    print("📚 API Documentation: http://localhost:8000/docs")
    print("🔍 Health Check: http://localhost:8000/health")
    print("⚠️  Press Ctrl+C to stop")
    print()

    try:
        # Create a simple FastAPI app for now to bypass complex import issues
        from fastapi import FastAPI
        from fastapi.middleware.cors import CORSMiddleware
        import uvicorn

        # Create simple app
        app = FastAPI(
            title="CTI Dashboard API",
            description="Cyber Threat Intelligence Dashboard - Simplified Version",
            version="1.0.0"
        )

        # Add CORS
        app.add_middleware(
            CORSMiddleware,
            allow_origins=["http://localhost:8080", "http://127.0.0.1:8080"],
            allow_credentials=True,
            allow_methods=["GET", "POST", "PUT", "DELETE"],
            allow_headers=["*"],
        )

        # Simple health check
        @app.get("/")
        async def root():
            return {"message": "CTI Dashboard API", "status": "running"}

        @app.get("/health")
        async def health():
            return {"status": "healthy", "version": "1.0.0"}

        # Watchlist endpoints (mock responses for now)
        @app.get("/watchlist/stats")
        async def get_watchlist_stats():
            return {
                "success": True,
                "total_items": 0,
                "active_items": 0,
                "total_alerts": 0,
                "unacknowledged_alerts": 0,
                "alert_severity_distribution": {
                    "low": 0,
                    "medium": 0,
                    "high": 0,
                    "critical": 0
                },
                "item_type_distribution": {
                    "ip": 0,
                    "domain": 0,
                    "hash": 0,
                    "email": 0,
                    "url": 0
                },
                "last_updated": "2025-01-06T00:00:00Z"
            }

        @app.get("/watchlist/alerts")
        async def get_watchlist_alerts(acknowledged: bool = False, limit: int = 10):
            return {
                "success": True,
                "total_alerts": 0,
                "alerts": [],
                "limit": limit,
                "acknowledged": acknowledged
            }

        # Actor endpoints (mock responses for now)
        @app.get("/actor/cyfirma/stats")
        async def get_cyfirma_stats():
            return {
                "success": True,
                "total_actors": 0,
                "last_updated": "2025-01-06T00:00:00Z",
                "actor_types": {},
                "motivations": {},
                "origin_countries": {},
                "severity_distribution": {},
                "top_target_industries": {},
                "top_target_countries": {}
            }

        @app.get("/actor/cyfirma/search")
        async def search_cyfirma_actors(limit: int = 100):
            return {
                "success": True,
                "total_count": 0,
                "actors": [],
                "facets": {},
                "query_time_ms": 0
            }

        @app.get("/actor/cyfirma/search/simple")
        async def search_cyfirma_by_name(name: str, limit: int = 50):
            return {
                "success": True,
                "total_count": 0,
                "actors": [],
                "query": name,
                "limit": limit
            }

        # Get configuration
        host = os.getenv('API_HOST', '127.0.0.1')
        port = int(os.getenv('API_PORT', '8000'))
        debug = os.getenv('DEBUG', 'false').lower() == 'true'
        log_level = os.getenv('LOG_LEVEL', 'info').lower()

        logger.info(f"🌐 Server configuration:")
        logger.info(f"   Host: {host}")
        logger.info(f"   Port: {port}")
        logger.info(f"   Debug: {debug}")
        logger.info(f"   Log Level: {log_level}")

        # Run the server
        uvicorn.run(
            app,
            host=host,
            port=port,
            log_level=log_level,
            reload=debug,
            access_log=True,
            use_colors=True
        )

    except ImportError as e:
        logger.error(f"❌ Failed to import application: {e}")
        logger.error("🔍 Make sure all dependencies are installed:")
        logger.error("   pip install -r requirements.txt")
        sys.exit(1)

    except KeyboardInterrupt:
        logger.info("\n👋 CTI Dashboard Backend stopped by user")

    except Exception as e:
        logger.error(f"❌ Unexpected error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()

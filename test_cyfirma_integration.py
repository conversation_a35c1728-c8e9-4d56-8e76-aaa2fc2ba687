#!/usr/bin/env python3
"""
Test script for Cyfirma API integration
"""

import asyncio
import json
import sys
import os

# Add the backend directory to Python path
backend_dir = os.path.join(os.path.dirname(__file__), 'src', 'backend')
if backend_dir not in sys.path:
    sys.path.insert(0, backend_dir)

from app.services.cyfirma_service import CyfirmaService

async def test_cyfirma_integration():
    """Test both STIX 1.x and 2.1 endpoints"""
    
    # Your API key
    api_key = "N3gxYJNBzm1OFxnwwMgAItdgLrNffYCU"
    
    print("🔍 Testing Cyfirma API Integration...")
    print("=" * 50)
    
    async with CyfirmaService(api_key) as cyfirma:
        
        # Test 1: STIX 2.1 - All threat actors
        print("\n📊 Test 1: STIX 2.1 - All Threat Actors")
        print("-" * 40)
        
        try:
            response = await cyfirma.fetch_all_threat_actors()
            if response.success:
                print(f"✅ Success: Found {response.total_count} threat actors")
                if response.data:
                    first_actor = response.data[0]
                    print(f"   Sample actor: {first_actor.name}")
                    print(f"   STIX ID: {first_actor.id}")
                    print(f"   Types: {[t.value for t in first_actor.threat_actor_types]}")
            else:
                print(f"❌ Failed: {response.error_message}")
        except Exception as e:
            print(f"❌ Exception: {str(e)}")
        
        # Test 2: STIX 2.1 - Search specific actor
        print("\n🔍 Test 2: STIX 2.1 - Search 'Fancy Bear'")
        print("-" * 40)
        
        try:
            response = await cyfirma.search_threat_actor("Fancy Bear")
            if response.success:
                print(f"✅ Success: Found {response.total_count} matching actors")
                if response.data:
                    actor = response.data[0]
                    print(f"   Actor: {actor.name}")
                    print(f"   Aliases: {actor.aliases}")
                    print(f"   Motivation: {actor.primary_motivation}")
            else:
                print(f"❌ Failed: {response.error_message}")
        except Exception as e:
            print(f"❌ Exception: {str(e)}")
        
        # Test 3: STIX 1.x - All threat actors
        print("\n📋 Test 3: STIX 1.x - All Threat Actors")
        print("-" * 40)
        
        try:
            response = await cyfirma.fetch_threat_actors_stix1()
            if response.success:
                print(f"✅ Success: Found {response.total_count} STIX 1.x actors")
                if response.data and len(response.data) > 0:
                    first_actor = response.data[0]
                    print(f"   Sample actor: {first_actor.get('title', 'Unknown')}")
                    print(f"   ID: {first_actor.get('id', 'Unknown')}")
                    if first_actor.get('motivations'):
                        motivations = [m.get('value', {}).get('value', 'Unknown') 
                                     for m in first_actor.get('motivations', [])]
                        print(f"   Motivations: {motivations}")
            else:
                print(f"❌ Failed: {response.error_message}")
        except Exception as e:
            print(f"❌ Exception: {str(e)}")
        
        # Test 4: Data processing
        print("\n⚙️ Test 4: Data Processing")
        print("-" * 40)
        
        try:
            response = await cyfirma.fetch_all_threat_actors()
            if response.success and response.data:
                actor = response.data[0]
                processed = cyfirma.process_threat_actor(actor)
                
                print(f"✅ Successfully processed actor: {processed.name}")
                print(f"   Confidence Score: {processed.confidence_score:.2f}")
                print(f"   Severity Level: {processed.severity_level}")
                print(f"   Target Countries: {processed.target_countries[:3]}...")  # First 3
                print(f"   Target Industries: {processed.target_industries[:3]}...")  # First 3
            else:
                print("❌ No data to process")
        except Exception as e:
            print(f"❌ Exception: {str(e)}")
    
    print("\n" + "=" * 50)
    print("🎯 Integration Test Complete!")

if __name__ == "__main__":
    asyncio.run(test_cyfirma_integration())
